<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold">Vendors</h2>
    </div>
    <div class="card">
        <div class="card-body p-0">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Shop Name</th>
                        <th>User</th>
                        <th>Status</th>
                        <th>Featured</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($vendor->shop_name); ?></td>
                        <td><?php echo e($vendor->user->name ?? ''); ?><br><small><?php echo e($vendor->user->email ?? ''); ?></small></td>
                        <td>
                            <?php if($vendor->is_approved): ?>
                                <span class="badge bg-success">Approved</span>
                            <?php else: ?>
                                <span class="badge bg-warning text-dark">Pending</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($vendor->is_featured): ?>
                                <span class="badge bg-primary">Featured</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Not Featured</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="<?php echo e(route('admin.vendors.show', $vendor)); ?>" class="btn btn-sm btn-dark me-2">View</a>
                            <a href="<?php echo e(route('admin.vendors.edit', $vendor)); ?>" class="btn btn-sm btn-outline-dark me-2">Edit</a>
                            <form action="<?php echo e(route('admin.vendors.destroy', $vendor)); ?>" method="POST" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-sm btn-outline-danger">Delete</button>
                            </form>
                            <?php if(!$vendor->is_approved): ?>
                                <form action="<?php echo e(route('admin.vendors.approve', $vendor->id)); ?>" method="POST" class="d-inline ms-2">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-sm btn-success">Approve</button>
                                </form>
                            <?php else: ?>
                                <form action="<?php echo e(route('admin.vendors.reject', $vendor->id)); ?>" method="POST" class="d-inline ms-2">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-sm btn-warning">Reject</button>
                                </form>
                            <?php endif; ?>
                            
                            <form action="<?php echo e(route('admin.vendors.toggle-featured', $vendor->id)); ?>" method="POST" class="d-inline ms-2">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-sm <?php echo e($vendor->is_featured ? 'btn-outline-primary' : 'btn-primary'); ?>">
                                    <?php echo e($vendor->is_featured ? 'Unfeature' : 'Feature'); ?>

                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="mt-3">
        <?php echo e($vendors->links()); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/admin/vendors/index.blade.php ENDPATH**/ ?>