<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e($title ?? config('app.name', 'Brandify')); ?></title>

    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Animate.css for UI effects -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

    <!-- Alpine.js CDN -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>

    <!-- Responsive fixes CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/responsive-fixes.css')); ?>">

    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: #000;
            --secondary-color: #fff;
            --text-color: #000;
            --bg-color: #fff;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
        }

        body {
            font-family: 'Figtree', 'Instrument Sans', sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: hidden;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: var(--gray-800);
            border-color: var(--gray-800);
        }

        .card {
            transition: all 0.3s ease;
            border: 1px solid var(--gray-300);
        }

        .card:hover {
            transform: translateY(-5px);
            border-color: var(--primary-color);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-link {
            color: var(--gray-700);
            font-weight: 500;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .footer {
            background-color: var(--gray-100);
            padding: 2rem 0;
            color: var(--gray-700);
        }

        .footer a {
            color: var(--gray-400);
            transition: color 0.2s ease;
        }

        .footer a:hover {
            color: var(--secondary-color);
            text-decoration: none;
        }

        /* Pagination Icon Size Fix */
        .pagination .page-link {
            font-size: 0.9rem;
            /* Adjust overall font size of pagination links */
        }

        .pagination .page-link svg {
            width: 1em;
            /* Or a specific size like 16px */
            height: 1em;
            /* Or a specific size like 16px */
        }

        .pagination .page-link i {
            /* If FontAwesome icons are used */
            font-size: 1em;
            /* Adjust icon size relative to page-link font-size */
            vertical-align: middle;
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
        }

        .pagination .page-link:hover {
            color: var(--primary-color);
        }

        /* Shop Page Specific Styles */
        .shop-header {
            background-color: var(--gray-100);
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--gray-300);
        }

        .shop-title {
            font-weight: 700;
            color: var(--primary-color);
        }

        /* Product Cards */
        .product-card {
            border: 1px solid #eee;
            /* Subtle border */
            transition: all 0.3s ease-in-out;
            background-color: #fff;
            height: 100%;
            border-radius: 8px;
            overflow: hidden;
        }

        .product-card:hover {
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        .product-card .card-img-top {
            aspect-ratio: 1 / 1;
            /* Maintain square aspect ratio */
            object-fit: cover;
            transition: transform 0.5s;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .product-card .card-title a {
            color: #212529;
            /* Bootstrap's default dark */
            font-weight: 600;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .product-card .card-title a:hover {
            color: #000 !important;
            /* Override if necessary, ensure black */
            text-decoration: none;
        }

        .product-card .card-text.text-muted a {
            color: #6c757d;
            /* Bootstrap's default muted */
        }

        /* Filter Sidebar (Basic Styling - to be expanded) */
        .filter-sidebar {
            background-color: var(--gray-100);
            padding: 1.5rem;
            border-radius: 0.25rem;
        }

        .filter-sidebar h5 {
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        /* Mobile Responsiveness Improvements */
        @media (max-width: 991.98px) {
            body {
                font-size: 0.95rem;
            }

            h1 {
                font-size: 1.8rem;
            }

            h2 {
                font-size: 1.5rem;
            }

            h3 {
                font-size: 1.3rem;
            }

            .container-fluid {
                padding-left: 15px;
                padding-right: 15px;
            }

            .navbar-collapse {
                background: white;
                padding: 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                margin-top: 0.5rem;
            }

            .navbar-nav {
                margin-bottom: 1rem;
            }

            form.d-flex {
                margin: 1rem 0;
                width: 100%;
            }

            .d-flex.align-items-center.gap-4 {
                display: flex;
                justify-content: space-around;
                width: 100%;
                margin-top: 0.5rem;
            }
        }

        @media (max-width: 767.98px) {
            .container.pt-5.mt-5 {
                padding-top: 4.5rem !important;
                margin-top: 3.5rem !important;
            }

            .card:hover {
                transform: none;
            }
        }

        /* Image handling and placeholders */
        img {
            max-width: 100%;
            height: auto;
        }

        img[src*="placeholder.com"] {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        /* Form controls enhancements */
        .form-control:focus,
        .btn:focus {
            box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1);
            border-color: #000;
        }

        /* Enhanced buttons */
        .btn-dark {
            background-color: #000;
            border-color: #000;
            transition: all 0.3s;
        }

        .btn-dark:hover {
            background-color: #333;
            border-color: #333;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-outline-dark {
            border-color: #000;
            color: #000;
            transition: all 0.3s;
        }

        .btn-outline-dark:hover {
            background-color: #000;
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Card improvements */
        .card {
            overflow: hidden;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        /* Footer improvements */
        .footer {
            padding-bottom: 4rem;
        }

        @media (max-width: 767.98px) {
            .footer h3 {
                margin-top: 1.5rem;
            }
        }
    </style>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Development Page Detector Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to detect Laravel error pages or exceptions
            function detectLaravelErrorPage() {
                // Check for common Laravel error indicators
                const hasLaravelError = (
                    document.querySelector('.symfony-error-detail') || // Symfony error detail
                    document.querySelector('.stack-viewer') || // Laravel stack trace
                    document.querySelector('div.exception-message') || // Exception message
                    document.querySelector('h1.laravel-error-heading') || // Laravel error heading
                    document.title.includes('Error') || // Page title contains Error
                    document.body.textContent.includes('Whoops, looks like something went wrong.') ||
                    // Whoops message
                    document.querySelector('div.flex.items-center.justify-center') // Laravel error centering
                );

                if (hasLaravelError) {
                    showDevelopmentModal();
                }
            }

            // Function to show the development modal
            function showDevelopmentModal() {
                // Hide error content
                document.body.innerHTML = '';
                document.body.style.overflow = 'hidden';

                // Create modal container
                const modalContainer = document.createElement('div');
                modalContainer.className = 'modal-backdrop';
                modalContainer.style.cssText =
                    'position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5);display:flex;justify-content:center;align-items:center;z-index:9999;';

                // Create modal content
                const modalContent = document.createElement('div');
                modalContent.className = 'modal-content bg-white';
                modalContent.style.cssText =
                    'width:90%;max-width:500px;padding:2rem;border-radius:8px;box-shadow:0 10px 25px rgba(0,0,0,0.1);text-align:center;';

                // Modal header with icon
                const icon = document.createElement('i');
                icon.className = 'fa-solid fa-tools fa-3x mb-3 text-warning';

                // Modal title
                const title = document.createElement('h2');
                title.textContent = 'Page Under Development';
                title.className = 'mb-3 fw-bold';

                // Modal message
                const message = document.createElement('p');
                message.textContent = 'We\'re currently working on this feature. Please check back soon!';
                message.className = 'mb-4 text-muted';

                // Back button
                const backButton = document.createElement('button');
                backButton.textContent = 'Go Back';
                backButton.className = 'btn btn-dark px-4 py-2';
                backButton.onclick = function() {
                    window.history.back();
                };

                // Home button
                const homeButton = document.createElement('a');
                homeButton.textContent = 'Home Page';
                homeButton.href = '/';
                homeButton.className = 'btn btn-outline-dark px-4 py-2 ms-2';

                // Append all elements
                modalContent.appendChild(icon);
                modalContent.appendChild(title);
                modalContent.appendChild(message);
                modalContent.appendChild(backButton);
                modalContent.appendChild(homeButton);
                modalContainer.appendChild(modalContent);
                document.body.appendChild(modalContainer);
            }

            // Run the detection
            detectLaravelErrorPage();

            // Also listen for AJAX errors that might indicate a Laravel exception
            window.addEventListener('error', function(event) {
                if (event.message && event.message.includes('Laravel') && event.message.includes('error')) {
                    showDevelopmentModal();
                }
            });
        });
    </script>
</head>

<body>
    <!-- Toast Container for Notifications -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
        <!-- Toasts will be appended here by JavaScript -->
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm py-2 py-lg-3">
        <div class="container-fluid px-3 px-sm-4 px-lg-5">
            <a class="navbar-brand fw-bold d-flex align-items-center" href="<?php echo e(route('home')); ?>">
                <img src="<?php echo e(asset('storage/brandify.png')); ?>" alt="Brandify Logo" class="me-2"
                    style="height: 32px; width: auto;"
                    onerror="this.onerror=null; this.src='https://via.placeholder.com/80x32?text=Brandify';">
                <span class="fs-4 d-none d-sm-inline"><?php echo e(config('app.name', 'Brandify')); ?></span>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link mx-3 fw-medium" href="<?php echo e(route('home')); ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link mx-3 fw-medium" href="<?php echo e(route('products.index')); ?>">Shop</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link mx-3 fw-medium" href="<?php echo e(route('about')); ?>">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link mx-3 fw-medium" href="<?php echo e(route('contact')); ?>">Contact</a>
                    </li>
                </ul>
                <!-- Search Form -->
                <form class="d-flex me-3 position-relative" action="<?php echo e(route('products.search')); ?>" method="GET"
                    id="ajaxSearchForm">
                    <div class="input-group">
                        <input class="form-control" type="search" name="query" id="ajaxSearchInput"
                            placeholder="Search products..." aria-label="Search" autocomplete="off" required>
                        <button class="btn btn-dark" type="submit"><i class="fas fa-search"></i></button>
                    </div>
                    <div id="ajaxSearchResults" class="position-absolute bg-white border shadow-sm rounded mt-1"
                        style="top: 100%; left: 0; right: 0; z-index: 1000; max-height: 300px; overflow-y: auto; display: none;">
                        <!-- AJAX search results will be populated here -->
                    </div>
                </form>
                <div class="d-flex align-items-center gap-4">
                    <?php if(auth()->guard()->check()): ?>
                        <div class="dropdown">
                            <a href="#" class="text-dark position-relative dropdown-toggle" id="userDropdown"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fa-regular fa-user fs-5"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><span class="dropdown-item-text"><?php echo e(auth()->user()->name); ?></span></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <?php if(auth()->user()->isAdmin()): ?>
                                    <li><a class="dropdown-item" href="<?php echo e(route('admin.dashboard')); ?>">Admin Dashboard</a>
                                    </li>
                                <?php endif; ?>
                                <?php if(auth()->user()->isVendor()): ?>
                                    <li><a class="dropdown-item" href="<?php echo e(route('vendor.dashboard')); ?>">Vendor
                                            Dashboard</a></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item" href="<?php echo e(route('dashboard')); ?>">My Account</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('wishlist.index')); ?>">My Wishlist</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('orders.index')); ?>">My Orders</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="dropdown-item">Logout</button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="text-dark position-relative" data-bs-toggle="tooltip"
                            title="Login">
                            <i class="fa-regular fa-user fs-5"></i>
                        </a>
                    <?php endif; ?>

                    <a href="<?php echo e(route('cart.index')); ?>" class="text-dark position-relative" data-bs-toggle="tooltip"
                        title="Cart">
                        <i class="fa-solid fa-cart-shopping fs-5"></i>
                        <span
                            class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-dark cart-count-badge">
                            <?php echo e(collect(session('cart', []))->sum('quantity')); ?>

                        </span>
                    </a>

                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('wishlist.index')); ?>" class="text-dark position-relative"
                            data-bs-toggle="tooltip" title="Wishlist">
                            <i class="fa-regular fa-heart fs-5"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container pt-5 mt-5 px-3 px-sm-4 px-md-5">
        <!-- Flash Messages -->
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo e(session('error')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if(session('info')): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <?php echo e(session('info')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if(session('warning')): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <?php echo e(session('warning')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous">
    </script>

    <!-- Initialize Bootstrap Tooltips -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>

    <!-- App JS -->
    <script src="<?php echo e(asset('js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('js/search.js')); ?>"></script>
    <script src="<?php echo e(asset('js/cart.js')); ?>"></script>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <?php echo $__env->make('layouts._footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?> 

</body>

</html>
<?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/layouts/app.blade.php ENDPATH**/ ?>