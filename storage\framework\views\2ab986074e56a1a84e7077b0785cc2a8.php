<?php $__env->startSection('title', 'Subscription Management'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Subscription Management</h1>
                </div>

                <!-- Subscription Statistics -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary"><?php echo e($stats['total']); ?></h5>
                                <p class="card-text small text-muted">Total Vendors</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?php echo e($stats['active']); ?></h5>
                                <p class="card-text small text-muted">Active</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?php echo e($stats['pending']); ?></h5>
                                <p class="card-text small text-muted">Pending</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-secondary"><?php echo e($stats['inactive']); ?></h5>
                                <p class="card-text small text-muted">Inactive</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger"><?php echo e($stats['cancelled']); ?></h5>
                                <p class="card-text small text-muted">Cancelled</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="<?php echo e(route('admin.subscriptions.index')); ?>" class="row g-3">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="Search vendors..."
                                    value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active
                                    </option>
                                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending
                                    </option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>
                                        Inactive</option>
                                    <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>
                                        Cancelled</option>
                                    <option value="suspended" <?php echo e(request('status') == 'suspended' ? 'selected' : ''); ?>>
                                        Suspended</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary">Filter</button>
                            </div>
                            <div class="col-md-3 text-end">
                                <a href="<?php echo e(route('admin.subscriptions.index')); ?>" class="btn btn-outline-secondary">Clear
                                    Filters</a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Subscriptions Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Vendor Subscriptions</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th>Vendor</th>
                                        <th>Email</th>
                                        <th>Shop Name</th>
                                        <th>Status</th>
                                        <th>Approved</th>
                                        <th>Joined</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="<?php echo e($vendor->logo ?? 'https://via.placeholder.com/40x40?text=V'); ?>"
                                                        alt="<?php echo e($vendor->shop_name); ?>" width="40" height="40"
                                                        class="rounded me-2">
                                                    <div>
                                                        <div class="fw-medium"><?php echo e($vendor->user->name); ?></div>
                                                        <small class="text-muted">ID: <?php echo e($vendor->id); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo e($vendor->user->email); ?></td>
                                            <td><?php echo e($vendor->shop_name); ?></td>
                                            <td>
                                                <?php
                                                    $statusColors = [
                                                        'active' => 'success',
                                                        'pending' => 'warning',
                                                        'inactive' => 'secondary',
                                                        'cancelled' => 'danger',
                                                        'suspended' => 'dark',
                                                    ];
                                                    $color = $statusColors[$vendor->subscription_status] ?? 'secondary';
                                                ?>
                                                <span
                                                    class="badge bg-<?php echo e($color); ?>"><?php echo e(ucfirst($vendor->subscription_status)); ?></span>
                                            </td>
                                            <td>
                                                <?php if($vendor->approved): ?>
                                                    <span class="badge bg-success">Yes</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($vendor->created_at->format('M d, Y')); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="<?php echo e(route('admin.subscriptions.edit', $vendor)); ?>"
                                                        class="btn btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if($vendor->subscription_status !== 'active'): ?>
                                                        <form action="<?php echo e(route('admin.subscriptions.activate', $vendor)); ?>"
                                                            method="POST" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('PATCH'); ?>
                                                            <button type="submit" class="btn btn-outline-success"
                                                                title="Activate">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    <?php if($vendor->subscription_status === 'active'): ?>
                                                        <form action="<?php echo e(route('admin.subscriptions.suspend', $vendor)); ?>"
                                                            method="POST" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('PATCH'); ?>
                                                            <button type="submit" class="btn btn-outline-warning"
                                                                title="Suspend">
                                                                <i class="fas fa-pause"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                                    <p>No vendors found.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php if($vendors->hasPages()): ?>
                        <div class="card-footer">
                            <?php echo e($vendors->appends(request()->query())->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/admin/subscriptions/index.blade.php ENDPATH**/ ?>