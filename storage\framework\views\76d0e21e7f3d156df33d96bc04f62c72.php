<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/layout-fixes.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="container py-4 store-page">
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>"
                                class="text-decoration-none text-dark">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('products.index')); ?>"
                                class="text-decoration-none text-dark">Shop</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo e($vendor->shop_name); ?></li>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Vendor Header -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        <div class="position-relative">
                            <img src="<?php echo e($vendor->banner_url ?? asset('images/default-banner.svg')); ?>"
                                alt="<?php echo e($vendor->shop_name); ?> Banner" class="img-fluid w-100"
                                style="height: 300px; object-fit: cover;">
                            <div class="position-absolute bottom-0 start-0 w-100 p-4"
                                style="background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);">
                                <div class="d-flex align-items-end">
                                    <img src="<?php echo e($vendor->logo_url ?? asset('images/default-logo.svg')); ?>"
                                        alt="<?php echo e($vendor->shop_name); ?> Logo"
                                        class="img-fluid rounded-circle border border-3 border-white"
                                        style="width: 100px; height: 100px; object-fit: cover;">
                                    <div class="ms-3 text-white">
                                        <h1 class="fw-bold mb-1"><?php echo e($vendor->shop_name); ?></h1>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <?php
                                                    $fullStars = floor($averageRating);
                                                    $hasHalfStar = $averageRating - $fullStars >= 0.5;
                                                    $emptyStars = 5 - $fullStars - ($hasHalfStar ? 1 : 0);
                                                ?>

                                                <?php for($i = 0; $i < $fullStars; $i++): ?>
                                                    <i class="fas fa-star text-warning"></i>
                                                <?php endfor; ?>

                                                <?php if($hasHalfStar): ?>
                                                    <i class="fas fa-star-half-alt text-warning"></i>
                                                <?php endif; ?>

                                                <?php for($i = 0; $i < $emptyStars; $i++): ?>
                                                    <i class="far fa-star text-warning"></i>
                                                <?php endfor; ?>

                                                <span class="ms-1"><?php echo e(number_format($averageRating, 1)); ?></span>
                                            </div>
                                            <span>(<?php echo e($totalReviews); ?> <?php echo e(Str::plural('review', $totalReviews)); ?>)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Vendor Info Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h5 class="fw-bold mb-3">About <?php echo e($vendor->shop_name); ?></h5>
                        <p><?php echo e($vendor->description); ?></p>

                        <hr>

                        <h6 class="fw-bold mb-3">Contact Information</h6>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <span><?php echo e($vendor->address); ?>, <?php echo e($vendor->city); ?>, <?php echo e($vendor->state); ?>,
                                <?php echo e($vendor->country); ?></span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-phone me-2"></i>
                            <span><?php echo e($vendor->phone); ?></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-envelope me-2"></i>
                            <span><?php echo e($vendor->user->email); ?></span>
                        </div>

                        <hr>

                        <h6 class="fw-bold mb-3">Store Statistics</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Products:</span>
                            <span class="fw-bold"><?php echo e($vendor->products->count()); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Joined:</span>
                            <span class="fw-bold"><?php echo e($vendor->created_at->format('M Y')); ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Response Rate:</span>
                            <span class="fw-bold">98%</span>
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="fw-bold mb-3">Categories</h5>
                        <div class="list-group list-group-flush">
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="#"
                                    class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <?php echo e($category->name); ?>

                                    <span class="badge bg-dark rounded-pill"><?php echo e($category->products_count); ?></span>
                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="fw-bold mb-0">Products (<?php echo e($products->total()); ?>)</h4>
                    <div class="d-flex align-items-center">
                        <label for="sort" class="me-2 mb-0">Sort by:</label>
                        <select class="form-select form-select-sm" id="sort" style="width: 200px;">
                            <option value="newest">Newest</option>
                            <option value="price_low">Price: Low to High</option>
                            <option value="price_high">Price: High to Low</option>
                            <option value="popular">Most Popular</option>
                            <option value="rating">Highest Rated</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <!-- Product Card -->
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-2 hover-border-dark">
                                <div class="position-relative">
                                    <a href="<?php echo e(route('products.show', $product->slug)); ?>">
                                        <img src="<?php echo e($product->image_url ?? asset('images/default-product.png')); ?>"
                                            alt="<?php echo e($product->name); ?>" class="card-img-top"
                                            style="aspect-ratio: 1 / 1; object-fit: cover;">
                                    </a>
                                    <?php if($product->isOnSale()): ?>
                                        <div
                                            class="position-absolute top-2 start-2 bg-dark text-white text-xs fw-bold px-2 py-1 rounded">
                                            SALE</div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body p-4">
                                    <p class="text-muted small mb-1"><?php echo e($product->category->name ?? 'Uncategorized'); ?></p>
                                    <h5 class="fw-semibold mb-1">
                                        <a href="<?php echo e(route('products.show', $product->slug)); ?>"
                                            class="text-decoration-none text-dark"><?php echo e(Str::limit($product->name, 45)); ?></a>
                                    </h5>
                                    <?php if($product->isOnSale()): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <p class="fw-bold me-2 mb-0">₦<?php echo e(number_format($product->discount_price, 2)); ?>

                                            </p>
                                            <p class="text-muted text-decoration-line-through mb-0">
                                                ₦<?php echo e(number_format($product->price, 2)); ?></p>
                                        </div>
                                    <?php else: ?>
                                        <p class="fw-bold mb-3">₦<?php echo e(number_format($product->price, 2)); ?></p>
                                    <?php endif; ?>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <form action="<?php echo e(route('cart.add', $product->id)); ?>" method="POST"
                                            class="ajax-add-to-cart-form w-100 me-2">
                                            <?php echo csrf_field(); ?>
                                            <input type="hidden" name="quantity" value="1">
                                            <button type="submit" class="btn btn-dark w-100 btn-sm add-to-cart-btn">
                                                <i class="fas fa-shopping-cart me-1"></i> Add to Cart
                                            </button>
                                        </form>
                                        <a href="#" class="text-dark fs-5"><i class="far fa-heart"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <p class="mb-0">No products found for this vendor.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($products->links()); ?>

                </div>
            </div>
        </div>

        <!-- Vendor Reviews -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="fw-bold mb-0">Customer Reviews</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-lg-4 mb-4 mb-lg-0">
                                <div class="text-center">
                                    <h1 class="display-1 fw-bold"><?php echo e(number_format($averageRating, 1)); ?></h1>
                                    <div class="mb-2">
                                        <?php
                                            $fullStars = floor($averageRating);
                                            $hasHalfStar = $averageRating - $fullStars >= 0.5;
                                            $emptyStars = 5 - $fullStars - ($hasHalfStar ? 1 : 0);
                                        ?>

                                        <?php for($i = 0; $i < $fullStars; $i++): ?>
                                            <i class="fas fa-star text-warning fs-4"></i>
                                        <?php endfor; ?>

                                        <?php if($hasHalfStar): ?>
                                            <i class="fas fa-star-half-alt text-warning fs-4"></i>
                                        <?php endif; ?>

                                        <?php for($i = 0; $i < $emptyStars; $i++): ?>
                                            <i class="far fa-star text-warning fs-4"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <p class="text-muted">Based on <?php echo e($totalReviews); ?>

                                        <?php echo e(Str::plural('review', $totalReviews)); ?></p>

                                    <?php if($totalReviews > 0): ?>
                                        <div class="mb-2">
                                            <?php for($rating = 5; $rating >= 1; $rating--): ?>
                                                <div class="d-flex align-items-center mb-1">
                                                    <span class="me-2"><?php echo e($rating); ?></span>
                                                    <div class="progress flex-grow-1" style="height: 8px;">
                                                        <div class="progress-bar bg-warning" role="progressbar"
                                                            style="width: <?php echo e($ratingDistribution[$rating]['percentage']); ?>%;"
                                                            aria-valuenow="<?php echo e($ratingDistribution[$rating]['percentage']); ?>"
                                                            aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                    <span
                                                        class="ms-2"><?php echo e($ratingDistribution[$rating]['count']); ?></span>
                                                </div>
                                            <?php endfor; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="col-lg-8">
                                <?php if($reviews->count() > 0): ?>
                                    <?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-2">
                                                    <h6 class="fw-bold mb-0"><?php echo e($review->user->name); ?></h6>
                                                    <small
                                                        class="text-muted"><?php echo e($review->created_at->format('M d, Y')); ?></small>
                                                </div>
                                                <div class="mb-2">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <?php if($i <= $review->rating): ?>
                                                            <i class="fas fa-star text-warning"></i>
                                                        <?php else: ?>
                                                            <i class="far fa-star text-warning"></i>
                                                        <?php endif; ?>
                                                    <?php endfor; ?>
                                                </div>
                                                <p class="mb-2"><strong>Product:</strong> <?php echo e($review->product->name); ?>

                                                </p>
                                                <p class="mb-0"><?php echo e($review->comment); ?></p>

                                                <?php if($review->vendor_response): ?>
                                                    <div class="mt-3 p-3 bg-light rounded">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <h6 class="fw-bold mb-0 text-primary">
                                                                <i class="fas fa-store me-1"></i><?php echo e($vendor->shop_name); ?>

                                                                Response
                                                            </h6>
                                                            <small
                                                                class="text-muted"><?php echo e($review->vendor_response_date->format('M d, Y')); ?></small>
                                                        </div>
                                                        <p class="mb-0"><?php echo e($review->vendor_response); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    <?php if($totalReviews > 3): ?>
                                        <div class="d-flex justify-content-center mt-4">
                                            <a href="#" class="btn btn-outline-dark">View All <?php echo e($totalReviews); ?>

                                                Reviews</a>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-star-o fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">No reviews yet</h6>
                                        <p class="text-muted">Be the first to review products from this vendor!</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/vendors/storefront.blade.php ENDPATH**/ ?>