<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/vendor-dashboard.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="vendor-dashboard">
        <!-- Subscription Alert -->
        <?php if(auth()->user()->vendor->needsSubscription()): ?>
            <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-1">Subscription Required</h5>
                        <p class="mb-2">
                            You have processed <?php echo e(auth()->user()->vendor->orders_processed); ?> orders and reached your free
                            limit of <?php echo e(auth()->user()->vendor->free_order_limit); ?> orders.
                            Subscribe now to continue processing orders.
                        </p>
                        <a href="<?php echo e(route('vendor.subscription.plans')); ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-crown me-1"></i> View Subscription Plans
                        </a>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php elseif(auth()->user()->vendor->getRemainingFreeOrders() <= 3 && auth()->user()->vendor->getRemainingFreeOrders() > 0): ?>
            <div class="alert alert-info alert-dismissible fade show mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle fa-2x me-3"></i>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading mb-1">Free Orders Running Low</h6>
                        <p class="mb-2">
                            You have <?php echo e(auth()->user()->vendor->getRemainingFreeOrders()); ?> free orders remaining.
                            Consider subscribing to get unlimited order processing.
                        </p>
                        <a href="<?php echo e(route('vendor.subscription.plans')); ?>" class="btn btn-info btn-sm">
                            <i class="fas fa-eye me-1"></i> View Plans
                        </a>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row g-3">
            <div class="col-6 col-md-3 mb-4">
                <div class="card h-100 dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">Total Sales</h6>
                                <h3 class="fw-bold mb-0"><?php echo '₦' . number_format($totalSales, 2); ?></h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign text-success"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <span
                                class="badge <?php echo e($salesGrowth >= 0 ? 'bg-success' : 'bg-danger'); ?>"><?php echo e($salesGrowth >= 0 ? '+' : ''); ?><?php echo e($salesGrowth); ?>%</span>
                            <span class="text-muted ms-2">from last month</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-6 col-md-3 mb-4">
                <div class="card h-100 dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">Total Orders</h6>
                                <h3 class="fw-bold mb-0"><?php echo e($totalOrders); ?></h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart text-primary"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <span
                                class="badge <?php echo e($orderGrowth >= 0 ? 'bg-success' : 'bg-danger'); ?>"><?php echo e($orderGrowth >= 0 ? '+' : ''); ?><?php echo e($orderGrowth); ?>%</span>
                            <span class="text-muted ms-2">from last month</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-6 col-md-3 mb-4">
                <div class="card h-100 dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">Total Products</h6>
                                <h3 class="fw-bold mb-0"><?php echo e($totalProducts); ?></h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-box text-info"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <span class="badge bg-success">+<?php echo e($newProductsThisMonth); ?></span>
                            <span class="text-muted ms-2">new this month</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-6 col-md-3 mb-4">
                <div class="card h-100 dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">Subscription</h6>
                                <h3 class="fw-bold mb-0"><?php echo e($subscriptionName); ?></h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-crown text-warning"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <?php if($daysRemaining > 0): ?>
                                <span class="badge bg-info"><?php echo e($daysRemaining); ?>

                                    <?php echo e(Str::plural('day', $daysRemaining)); ?></span>
                                <span class="text-muted ms-2">remaining</span>
                            <?php elseif($daysRemaining == 0): ?>
                                <span class="badge bg-warning">Expires today</span>
                                <a href="<?php echo e(route('vendor.subscription.plans')); ?>" class="text-muted ms-2">Renew now</a>
                            <?php else: ?>
                                <span class="badge bg-danger">Expired</span>
                                <a href="<?php echo e(route('vendor.subscription.plans')); ?>" class="text-muted ms-2">Renew now</a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Dashboard Charts -->
        <div class="row mt-4 mb-4 g-3">
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Revenue & Orders Overview</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-dark dropdown-toggle" type="button"
                                id="chartTimeframeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Last 6 Months
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="chartTimeframeDropdown">
                                <li><a class="dropdown-item chart-timeframe" data-period="week" href="#">This Week</a>
                                </li>
                                <li><a class="dropdown-item chart-timeframe" data-period="month" href="#">This
                                        Month</a></li>
                                <li><a class="dropdown-item chart-timeframe active" data-period="6months"
                                        href="#">Last
                                        6 Months</a></li>
                                <li><a class="dropdown-item chart-timeframe" data-period="year" href="#">This
                                        Year</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                        <div id="chart-error-message" class="alert alert-danger text-center mt-3 mb-0 p-2"
                            style="display:none; font-size: 0.95rem;">
                            <i class="fas fa-exclamation-triangle me-2"></i>Unable to load analytics data. Showing
                            simulated data instead.
                        </div>
                    </div>
                </div>

                <div class="row g-3">
                    <div class="col-12 col-sm-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">Product Categories</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container small">
                                    <canvas id="categoryChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">Customer Growth</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container small">
                                    <canvas id="customerChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Nigeria Orders Map Widget -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Nigeria Orders Map</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-dark dropdown-toggle" type="button"
                                id="mapTimeframeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Last 30 Days
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="mapTimeframeDropdown">
                                <li><a class="dropdown-item map-timeframe" data-timeframe="7" href="#">Last 7
                                        Days</a></li>
                                <li><a class="dropdown-item map-timeframe active" data-timeframe="30" href="#">Last
                                        30 Days</a></li>
                                <li><a class="dropdown-item map-timeframe" data-timeframe="90" href="#">Last 90
                                        Days</a></li>
                                <li><a class="dropdown-item map-timeframe" data-timeframe="all" href="#">All
                                        Time</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="nigeria-map-container" class="position-relative">
                            <div id="nigeria-map" style="height: 100%;"></div>
                            <div id="map-loading-overlay"
                                class="position-absolute d-flex align-items-center justify-content-center bg-white bg-opacity-75"
                                style="top: 0; left: 0; right: 0; bottom: 0; z-index: 10; display: none;">
                                <div class="spinner-border text-dark" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                            <div id="map-error-message"
                                class="position-absolute w-100 h-100 d-flex align-items-center justify-content-center bg-white bg-opacity-75"
                                style="top: 0; left: 0; right: 0; bottom: 0; z-index: 11; display: none;">
                                <div class="text-center">
                                    <i class="fas fa-exclamation-triangle text-danger mb-2" style="font-size: 2rem;"></i>
                                    <p class="mb-0 text-dark">Unable to load Nigeria Orders Map data.<br>Showing sample
                                        data instead.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer p-0">
                        <div class="table-responsive" style="max-height: 200px; overflow-y: auto;">
                            <table class="table table-sm table-hover mb-0">
                                <thead>
                                    <tr class="table-light">
                                        <th class="ps-3">State</th>
                                        <th>Orders</th>
                                        <th class="pe-3">Value</th>
                                    </tr>
                                </thead>
                                <tbody id="state-orders-table">
                                    <tr>
                                        <td colspan="3" class="text-center py-3">
                                            <div class="spinner-border spinner-border-sm" role="status"></div> Loading...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-12 col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Orders</h5>
                        <a href="<?php echo e(route('vendor.orders.index')); ?>" class="btn btn-sm btn-dark">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>#<?php echo e($order->id); ?></td>
                                            <td><?php echo e($order->user->name ?? 'Guest'); ?></td>
                                            <td><?php echo e($order->created_at->format('M d, Y')); ?></td>
                                            <td>
                                                <?php
                                                    $orderTotal = 0;
                                                    foreach ($order->items as $item) {
                                                        if (
                                                            $item->product &&
                                                            $item->product->vendor_id == auth()->user()->vendor->id
                                                        ) {
                                                            $orderTotal += $item->price * $item->quantity;
                                                        }
                                                    }
                                                ?>
                                                <?php echo '₦' . number_format($orderTotal, 2); ?>
                                            </td>
                                            <td>
                                                <?php if($order->status == 'completed'): ?>
                                                    <span class="badge bg-success">Completed</span>
                                                <?php elseif($order->status == 'processing'): ?>
                                                    <span class="badge bg-warning text-dark">Processing</span>
                                                <?php elseif($order->status == 'shipping'): ?>
                                                    <span class="badge bg-info">Shipping</span>
                                                <?php elseif($order->status == 'cancelled'): ?>
                                                    <span class="badge bg-danger">Cancelled</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?php echo e(ucfirst($order->status)); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('vendor.orders.show', $order->id)); ?>"
                                                    class="btn btn-sm btn-outline-dark">View</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="6" class="text-center">No recent orders found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-lg-4 mb-4">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Top Products</h5>
                        <a href="<?php echo e(route('vendor.products.index')); ?>" class="btn btn-sm btn-dark">View All</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php $__empty_1 = true; $__currentLoopData = $productPerformance; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <?php if($product->image_url): ?>
                                                <img src="<?php echo e($product->image_url); ?>" alt="<?php echo e($product->name); ?>"
                                                    class="rounded me-2"
                                                    style="width: 40px; height: 40px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center"
                                                    style="width: 40px; height: 40px;">
                                                    <i class="fas fa-box text-secondary"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($product->name); ?></h6>
                                                <small class="text-muted"><?php echo '₦' . number_format($product->price, 2); ?></small>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold"><?php echo e($product->total_sold); ?></div>
                                            <small class="text-muted">units sold</small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="list-group-item text-center py-3">
                                    <p class="mb-0 text-muted">No product data available yet</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('vendor.products.create')); ?>" class="btn btn-outline-dark">
                                <i class="fas fa-plus me-2"></i> Add New Product
                            </a>
                            <a href="<?php echo e(route('vendor.orders.index')); ?>" class="btn btn-outline-dark">
                                <i class="fas fa-shopping-cart me-2"></i> Manage Orders
                            </a>
                            <a href="<?php echo e(route('vendor.settings')); ?>" class="btn btn-outline-dark">
                                <i class="fas fa-store-alt me-2"></i> Shop Settings
                            </a>
                            <a href="<?php echo e(route('vendor.subscription.status')); ?>" class="btn btn-outline-dark">
                                <i class="fas fa-crown me-2"></i> Manage Subscription
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="<?php echo e(asset('js/nigeria-map.js')); ?>"></script>
        <script src="<?php echo e(asset('js/vendor-dashboard-new.js')); ?>"></script>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/vendor/dashboard.blade.php ENDPATH**/ ?>